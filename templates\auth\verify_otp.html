{% extends "base.html" %}

{% block title %}Verify Login Code - School Management System{% endblock %}

{% block content %}
<div class="login-container">
    <div class="otp-verification-form">
        <div class="otp-header">
            <h2>🔐 Verify Your Identity</h2>
            <p class="text-muted">We've sent a verification code to <strong>{{ user.email }}</strong></p>
        </div>

        <form method="post" action="{{ url_for('auth.verify_otp') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            
            <div class="form-group">
                <label for="otp_code">Enter 6-digit verification code:</label>
                <input type="text" 
                       id="otp_code" 
                       name="otp_code" 
                       class="form-control otp-input" 
                       maxlength="6" 
                       pattern="[0-9]{6}"
                       placeholder="000000"
                       autocomplete="one-time-code"
                       required
                       autofocus>
                <small class="form-text text-muted">
                    Check your email inbox and spam folder
                </small>
            </div>

            <div class="form-group button-group">
                <button type="submit" name="submit" class="btn btn-primary animated-btn">
                    <i class="fas fa-check"></i> Verify Code
                </button>
                <button type="submit" name="resend" class="btn btn-outline-secondary">
                    <i class="fas fa-redo"></i> Resend Code
                </button>
            </div>
        </form>

        <div class="otp-info">
            <div class="alert alert-info">
                <h6><i class="fas fa-info-circle"></i> Important Information:</h6>
                <ul class="mb-0">
                    <li>The verification code expires in <strong>5 minutes</strong></li>
                    <li>You have <strong>3 attempts</strong> to enter the correct code</li>
                    <li>If you don't receive the code, check your spam folder</li>
                    <li>Never share this code with anyone</li>
                </ul>
            </div>
        </div>

        <div class="otp-help">
            <p class="text-center">
                <a href="{{ url_for('auth.login') }}" class="btn btn-link">
                    <i class="fas fa-arrow-left"></i> Back to Login
                </a>
            </p>
        </div>
    </div>
</div>

<style>
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 80vh;
    padding: 20px;
}

.otp-verification-form {
    background: white;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-width: 500px;
    width: 100%;
}

.otp-header {
    text-align: center;
    margin-bottom: 30px;
}

.otp-header h2 {
    color: #007bff;
    margin-bottom: 10px;
}

.otp-input {
    font-size: 24px;
    text-align: center;
    letter-spacing: 8px;
    font-weight: bold;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    transition: border-color 0.3s ease;
}

.otp-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    outline: none;
}

.button-group {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin: 20px 0;
}

.button-group .btn {
    flex: 1;
    max-width: 150px;
}

.animated-btn {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
    color: white;
    padding: 12px 20px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.animated-btn:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.otp-info {
    margin: 20px 0;
}

.otp-info .alert {
    border-radius: 8px;
}

.otp-info ul {
    padding-left: 20px;
}

.otp-info li {
    margin-bottom: 5px;
}

.otp-help {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

/* Responsive design */
@media (max-width: 576px) {
    .otp-verification-form {
        padding: 20px;
        margin: 10px;
    }
    
    .button-group {
        flex-direction: column;
    }
    
    .button-group .btn {
        max-width: none;
        margin-bottom: 10px;
    }
    
    .otp-input {
        font-size: 20px;
        letter-spacing: 4px;
    }
}

/* Auto-focus and input formatting */
.otp-input::-webkit-outer-spin-button,
.otp-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.otp-input[type=number] {
    -moz-appearance: textfield;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const otpInput = document.getElementById('otp_code');
    
    // Format input as user types
    otpInput.addEventListener('input', function(e) {
        // Remove any non-digit characters
        let value = e.target.value.replace(/\D/g, '');
        
        // Limit to 6 digits
        if (value.length > 6) {
            value = value.substring(0, 6);
        }
        
        e.target.value = value;
        
        // Auto-submit when 6 digits are entered
        if (value.length === 6) {
            // Small delay to allow user to see the complete code
            setTimeout(function() {
                document.querySelector('button[name="submit"]').click();
            }, 500);
        }
    });
    
    // Prevent non-numeric input
    otpInput.addEventListener('keypress', function(e) {
        if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
            e.preventDefault();
        }
    });
    
    // Handle paste events
    otpInput.addEventListener('paste', function(e) {
        e.preventDefault();
        const paste = (e.clipboardData || window.clipboardData).getData('text');
        const digits = paste.replace(/\D/g, '').substring(0, 6);
        e.target.value = digits;
        
        // Auto-submit if 6 digits pasted
        if (digits.length === 6) {
            setTimeout(function() {
                document.querySelector('button[name="submit"]').click();
            }, 500);
        }
    });
    
    // Auto-focus the input
    otpInput.focus();
});
</script>
{% endblock %}
