#!/usr/bin/env python3
"""
OTP System Setup Script for Flask School Management System
---------------------------------------------------------
This script helps set up and configure the OTP verification system.
"""

import sys
import os
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_requirements():
    """Check if all required packages are installed."""
    print("📦 Checking requirements...")
    
    required_packages = [
        'flask',
        'flask-mail',
        'flask-login',
        'flask-wtf',
        'flask-sqlalchemy',
        'wtforms',
        'email-validator'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Please install them using:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ All required packages are installed!")
    return True

def setup_database():
    """Set up database tables for OTP system."""
    print("\n🗄️  Setting up database...")
    
    try:
        from app import create_app
        from models import db
        
        app = create_app()
        
        with app.app_context():
            # Create all tables
            db.create_all()
            print("✅ Database tables created successfully!")
            
            # Check if OTP table exists
            from models import OTP
            otp_count = OTP.query.count()
            print(f"📊 Current OTP records: {otp_count}")
            
        return True
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

def test_email_configuration():
    """Test email configuration."""
    print("\n📧 Testing email configuration...")
    
    try:
        from app import create_app
        from email_service import test_email_configuration
        
        app = create_app()
        
        with app.app_context():
            # Check configuration
            mail_server = app.config.get('MAIL_SERVER')
            mail_username = app.config.get('MAIL_USERNAME')
            mail_password = app.config.get('MAIL_PASSWORD')
            
            print(f"  Mail Server: {mail_server}")
            print(f"  Mail Username: {mail_username}")
            print(f"  Mail Password: {'*' * len(mail_password) if mail_password else 'Not set'}")
            
            if not mail_username or not mail_password:
                print("⚠️  Email credentials not configured!")
                print("Please set the following environment variables:")
                print("  - MAIL_USERNAME: Your email address")
                print("  - MAIL_PASSWORD: Your email password or app password")
                return False
            
            # Test email sending
            if test_email_configuration():
                print("✅ Email configuration test passed!")
                return True
            else:
                print("❌ Email configuration test failed!")
                return False
                
    except Exception as e:
        print(f"❌ Email configuration test error: {e}")
        return False

def run_system_test():
    """Run the OTP system test."""
    print("\n🧪 Running OTP system test...")
    
    try:
        from test_otp_system import run_integration_test
        return run_integration_test()
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

def show_configuration_guide():
    """Show configuration guide for the OTP system."""
    print("\n" + "="*60)
    print("📋 OTP SYSTEM CONFIGURATION GUIDE")
    print("="*60)
    
    print("""
1. EMAIL CONFIGURATION:
   Set the following environment variables:
   
   For Gmail:
   export MAIL_SERVER=smtp.gmail.com
   export MAIL_PORT=587
   export MAIL_USE_TLS=true
   export MAIL_USERNAME=<EMAIL>
   export MAIL_PASSWORD=your-app-password
   export MAIL_DEFAULT_SENDER=<EMAIL>
   
   For other email providers, adjust MAIL_SERVER and MAIL_PORT accordingly.

2. SECURITY SETTINGS:
   The following settings can be adjusted in config.py:
   - OTP_EXPIRY_MINUTES: How long OTPs are valid (default: 5 minutes)
   - OTP_LENGTH: Length of OTP codes (default: 6 digits)
   - MAX_OTP_ATTEMPTS: Maximum verification attempts (default: 3)

3. PERIODIC CLEANUP:
   Run the cleanup task periodically to remove expired OTPs:
   python otp_cleanup_task.py --cleanup
   
   Or set up a cron job:
   */15 * * * * cd /path/to/your/app && python otp_cleanup_task.py --cleanup

4. MONITORING:
   Check OTP statistics:
   python otp_cleanup_task.py --stats
   
   View logs in otp_cleanup.log for security events.

5. TESTING:
   Test the complete system:
   python test_otp_system.py
   
   Test email configuration:
   python otp_cleanup_task.py --test-email
""")
    
    print("="*60)

def main():
    """Main setup function."""
    print("🚀 OTP System Setup for Flask School Management System")
    print("="*60)
    
    success = True
    
    # Check requirements
    if not check_requirements():
        success = False
    
    # Setup database
    if success and not setup_database():
        success = False
    
    # Test email configuration
    if success:
        email_success = test_email_configuration()
        if not email_success:
            print("⚠️  Email configuration issues detected, but setup can continue.")
    
    # Show configuration guide
    show_configuration_guide()
    
    # Ask if user wants to run tests
    if success:
        try:
            run_tests = input("\n🧪 Would you like to run the system tests? (y/N): ").lower().strip()
            if run_tests in ['y', 'yes']:
                test_success = run_system_test()
                if test_success:
                    print("\n✅ Setup completed successfully!")
                else:
                    print("\n⚠️  Setup completed but some tests failed.")
            else:
                print("\n✅ Setup completed! You can run tests later with: python test_otp_system.py")
        except KeyboardInterrupt:
            print("\n\n✅ Setup completed!")
    
    if success:
        print("\n🎉 OTP system is ready to use!")
        print("Users will now receive verification codes via email when logging in.")
    else:
        print("\n❌ Setup encountered issues. Please resolve them before using the OTP system.")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
