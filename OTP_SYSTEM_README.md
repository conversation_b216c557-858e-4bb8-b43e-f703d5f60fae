# OTP Verification System for Flask School Management System

## Overview

This OTP (One-Time Password) verification system adds an extra layer of security to the login process. After users enter their username and password, they receive a 6-digit verification code via email that must be entered to complete the login.

## Features

- ✅ **Secure OTP Generation**: 6-digit random codes using cryptographically secure methods
- ✅ **Email Integration**: Beautiful HTML emails with verification codes
- ✅ **Time-based Expiry**: OTPs expire after 5 minutes (configurable)
- ✅ **Attempt Limiting**: Maximum 3 verification attempts per OTP
- ✅ **Rate Limiting**: Prevents abuse with IP-based rate limiting
- ✅ **Auto-cleanup**: Automatic removal of expired OTPs
- ✅ **Security Logging**: Comprehensive logging of security events
- ✅ **Responsive UI**: Mobile-friendly verification interface
- ✅ **Auto-submit**: Automatic form submission when 6 digits are entered

## Installation & Setup

### 1. Install Dependencies

```bash
pip install Flask-Mail==0.9.1
```

### 2. Configure Email Settings

Set the following environment variables:

```bash
# For Gmail
export MAIL_SERVER=smtp.gmail.com
export MAIL_PORT=587
export MAIL_USE_TLS=true
export MAIL_USERNAME=<EMAIL>
export MAIL_PASSWORD=your-app-password
export MAIL_DEFAULT_SENDER=<EMAIL>
```

**Note**: For Gmail, you'll need to use an "App Password" instead of your regular password.

### 3. Run Setup Script

```bash
python setup_otp_system.py
```

This will:
- Check all dependencies
- Set up database tables
- Test email configuration
- Run system tests
- Show configuration guide

## Usage

### User Experience

1. User enters username and password on login page
2. If credentials are correct, user is redirected to OTP verification page
3. User receives email with 6-digit verification code
4. User enters code on verification page
5. Upon successful verification, user is logged in and redirected to dashboard

### Admin Features

- **Statistics**: View OTP usage statistics
- **Cleanup**: Remove expired OTPs
- **Monitoring**: Security event logging

## Configuration Options

Edit `config.py` to customize:

```python
# OTP configuration
OTP_EXPIRY_MINUTES = 5  # OTP expires after 5 minutes
OTP_LENGTH = 6  # 6-digit OTP
MAX_OTP_ATTEMPTS = 3  # Maximum attempts before blocking
```

## File Structure

```
├── models.py                 # OTP database model
├── email_service.py          # Email sending functionality
├── otp_security.py          # Security features and rate limiting
├── blueprints/auth.py       # Updated authentication routes
├── templates/auth/verify_otp.html  # OTP verification page
├── otp_cleanup_task.py      # Periodic cleanup script
├── test_otp_system.py       # Comprehensive tests
├── setup_otp_system.py      # Setup and configuration script
└── OTP_SYSTEM_README.md     # This file
```

## Management Commands

### Run Cleanup Task
```bash
python otp_cleanup_task.py --cleanup
```

### View Statistics
```bash
python otp_cleanup_task.py --stats
```

### Test Email Configuration
```bash
python otp_cleanup_task.py --test-email
```

### Run All Tasks
```bash
python otp_cleanup_task.py --all
```

## Testing

### Run Complete Test Suite
```bash
python test_otp_system.py
```

### Manual Testing
1. Try logging in with valid credentials
2. Check email for verification code
3. Enter code on verification page
4. Test with invalid codes
5. Test code expiry (wait 5+ minutes)

## Security Features

### Rate Limiting
- Maximum 5 OTP requests per 15-minute window per IP/user
- Automatic blocking of excessive requests

### Attempt Limiting
- Maximum 3 verification attempts per OTP
- OTP invalidated after max attempts reached

### Security Logging
- All OTP events are logged with timestamps
- Failed attempts and security violations tracked
- IP addresses and user agents recorded

### Auto-cleanup
- Expired OTPs automatically removed from database
- Configurable cleanup intervals
- Statistics tracking for monitoring

## Troubleshooting

### Email Not Sending
1. Check environment variables are set correctly
2. Verify email credentials (use app password for Gmail)
3. Check firewall/network restrictions
4. Test with: `python otp_cleanup_task.py --test-email`

### OTP Not Working
1. Check database tables exist: `python setup_otp_system.py`
2. Verify OTP hasn't expired (5-minute limit)
3. Check for rate limiting
4. Review logs for error messages

### Database Issues
1. Run setup script: `python setup_otp_system.py`
2. Check database permissions
3. Verify SQLAlchemy configuration

## Production Deployment

### Environment Variables
Set these in your production environment:
```bash
MAIL_SERVER=your-smtp-server
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_DEFAULT_SENDER=<EMAIL>
```

### Cron Job for Cleanup
Add to crontab for automatic cleanup:
```bash
*/15 * * * * cd /path/to/your/app && python otp_cleanup_task.py --cleanup
```

### Monitoring
- Monitor `otp_cleanup.log` for security events
- Set up alerts for excessive failed attempts
- Regular statistics review

## API Reference

### OTP Model Methods
- `OTP.generate_otp(length=6)`: Generate secure OTP code
- `OTP.is_valid()`: Check if OTP is still valid
- `OTP.verify(code)`: Verify provided code
- `OTP.cleanup_expired()`: Remove expired OTPs

### Email Service Functions
- `send_otp_email(user_id, otp_code)`: Send OTP via email
- `create_and_send_otp(user_id)`: Create and send new OTP
- `test_email_configuration()`: Test email setup

### Security Functions
- `validate_otp_security(user_id, code)`: Secure OTP validation
- `log_security_event(event_type, user_id, details)`: Log security events
- `cleanup_expired_otps()`: Database cleanup

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review log files for error messages
3. Run the test suite to identify problems
4. Verify configuration with setup script

## License

This OTP system is part of the Flask School Management System and follows the same license terms.
