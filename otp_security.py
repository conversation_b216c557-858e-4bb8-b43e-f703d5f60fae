"""
OTP Security Module for Flask School Management System
----------------------------------------------------
Handles OTP cleanup, rate limiting, and security measures.
"""

from flask import session, request, current_app
from models import OTP, User, db
from datetime import datetime, timedelta
import logging
from functools import wraps
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OTPRateLimiter:
    """Rate limiter for OTP requests to prevent abuse."""
    
    def __init__(self):
        self.attempts = {}  # Store attempts per IP/user
        self.cleanup_interval = 300  # Clean up old entries every 5 minutes
        self.last_cleanup = time.time()
    
    def _cleanup_old_entries(self):
        """Remove old rate limiting entries."""
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            cutoff_time = current_time - 3600  # Remove entries older than 1 hour
            keys_to_remove = [key for key, data in self.attempts.items() 
                            if data['last_attempt'] < cutoff_time]
            for key in keys_to_remove:
                del self.attempts[key]
            self.last_cleanup = current_time
    
    def is_rate_limited(self, identifier, max_attempts=5, window_minutes=15):
        """
        Check if an identifier (IP or user) is rate limited.
        
        Args:
            identifier (str): IP address or user ID
            max_attempts (int): Maximum attempts allowed
            window_minutes (int): Time window in minutes
            
        Returns:
            tuple: (is_limited: bool, remaining_attempts: int, reset_time: datetime)
        """
        self._cleanup_old_entries()
        
        current_time = time.time()
        window_seconds = window_minutes * 60
        
        if identifier not in self.attempts:
            self.attempts[identifier] = {
                'count': 0,
                'first_attempt': current_time,
                'last_attempt': current_time
            }
        
        attempt_data = self.attempts[identifier]
        
        # Reset counter if window has passed
        if current_time - attempt_data['first_attempt'] > window_seconds:
            attempt_data['count'] = 0
            attempt_data['first_attempt'] = current_time
        
        is_limited = attempt_data['count'] >= max_attempts
        remaining = max(0, max_attempts - attempt_data['count'])
        reset_time = datetime.fromtimestamp(attempt_data['first_attempt'] + window_seconds)
        
        return is_limited, remaining, reset_time
    
    def record_attempt(self, identifier):
        """Record an OTP request attempt."""
        self._cleanup_old_entries()
        
        current_time = time.time()
        
        if identifier not in self.attempts:
            self.attempts[identifier] = {
                'count': 0,
                'first_attempt': current_time,
                'last_attempt': current_time
            }
        
        self.attempts[identifier]['count'] += 1
        self.attempts[identifier]['last_attempt'] = current_time

# Global rate limiter instance
rate_limiter = OTPRateLimiter()

def rate_limit_otp_requests(max_attempts=5, window_minutes=15):
    """
    Decorator to rate limit OTP requests.
    
    Args:
        max_attempts (int): Maximum attempts allowed
        window_minutes (int): Time window in minutes
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Use IP address as identifier
            identifier = request.remote_addr or 'unknown'
            
            # Add user ID if available in session
            if 'pending_user_id' in session:
                identifier = f"{identifier}_{session['pending_user_id']}"
            
            is_limited, remaining, reset_time = rate_limiter.is_rate_limited(
                identifier, max_attempts, window_minutes
            )
            
            if is_limited:
                logger.warning(f"Rate limit exceeded for {identifier}")
                return {
                    'error': 'Too many requests',
                    'message': f'Rate limit exceeded. Try again after {reset_time.strftime("%H:%M:%S")}',
                    'reset_time': reset_time
                }, 429
            
            # Record the attempt
            rate_limiter.record_attempt(identifier)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def cleanup_expired_otps():
    """
    Clean up expired OTP records from the database.
    
    Returns:
        int: Number of expired OTPs removed
    """
    try:
        expired_count = OTP.cleanup_expired()
        if expired_count > 0:
            logger.info(f"Cleaned up {expired_count} expired OTP records")
        return expired_count
    except Exception as e:
        logger.error(f"Error during OTP cleanup: {e}")
        return 0

def cleanup_old_sessions():
    """Clean up old login sessions that weren't completed."""
    try:
        # This would typically be handled by Flask's session management
        # but we can add custom logic here if needed
        logger.info("Session cleanup completed")
        return True
    except Exception as e:
        logger.error(f"Error during session cleanup: {e}")
        return False

def validate_otp_security(user_id, provided_code):
    """
    Validate OTP with additional security checks.
    
    Args:
        user_id (int): User ID
        provided_code (str): OTP code provided by user
        
    Returns:
        dict: Validation result with success status and message
    """
    try:
        # Basic input validation
        if not provided_code or not provided_code.isdigit() or len(provided_code) != 6:
            return {
                'success': False,
                'message': 'Invalid code format. Please enter a 6-digit code.',
                'code': 'INVALID_FORMAT'
            }
        
        # Get the most recent valid OTP
        otp = OTP.get_valid_otp(user_id)
        
        if not otp:
            return {
                'success': False,
                'message': 'No valid verification code found. Please request a new one.',
                'code': 'NO_VALID_OTP'
            }
        
        # Check if OTP has expired
        if not otp.is_valid():
            return {
                'success': False,
                'message': 'Verification code has expired. Please request a new one.',
                'code': 'EXPIRED'
            }
        
        # Check attempt limits
        if otp.attempts >= current_app.config.get('MAX_OTP_ATTEMPTS', 3):
            # Mark OTP as used to prevent further attempts
            otp.is_used = True
            db.session.commit()
            return {
                'success': False,
                'message': 'Too many failed attempts. Please request a new verification code.',
                'code': 'TOO_MANY_ATTEMPTS'
            }
        
        # Verify the OTP
        if otp.verify(provided_code):
            return {
                'success': True,
                'message': 'Verification successful',
                'code': 'SUCCESS'
            }
        else:
            return {
                'success': False,
                'message': f'Invalid verification code. {current_app.config.get("MAX_OTP_ATTEMPTS", 3) - otp.attempts} attempts remaining.',
                'code': 'INVALID_CODE'
            }
            
    except Exception as e:
        logger.error(f"Error validating OTP for user {user_id}: {e}")
        return {
            'success': False,
            'message': 'An error occurred during verification. Please try again.',
            'code': 'SYSTEM_ERROR'
        }

def log_security_event(event_type, user_id=None, details=None):
    """
    Log security-related events for monitoring.
    
    Args:
        event_type (str): Type of security event
        user_id (int): User ID if applicable
        details (dict): Additional event details
    """
    try:
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'event_type': event_type,
            'user_id': user_id,
            'ip_address': request.remote_addr if request else 'unknown',
            'user_agent': request.headers.get('User-Agent') if request else 'unknown',
            'details': details or {}
        }
        
        logger.info(f"Security Event: {log_entry}")
        
        # In a production environment, you might want to store these logs
        # in a separate security log file or send them to a monitoring service
        
    except Exception as e:
        logger.error(f"Error logging security event: {e}")

def schedule_cleanup_tasks():
    """
    Schedule periodic cleanup tasks.
    This should be called periodically (e.g., via a cron job or background task).
    """
    try:
        # Clean up expired OTPs
        expired_otps = cleanup_expired_otps()
        
        # Clean up old sessions
        cleanup_old_sessions()
        
        # Log cleanup results
        log_security_event('CLEANUP_COMPLETED', details={
            'expired_otps_removed': expired_otps
        })
        
        return True
        
    except Exception as e:
        logger.error(f"Error during scheduled cleanup: {e}")
        log_security_event('CLEANUP_ERROR', details={'error': str(e)})
        return False

def get_otp_statistics():
    """
    Get OTP usage statistics for monitoring.
    
    Returns:
        dict: Statistics about OTP usage
    """
    try:
        from sqlalchemy import func
        
        stats = {
            'total_otps_generated': OTP.query.count(),
            'active_otps': OTP.query.filter_by(is_used=False).filter(
                OTP.expires_at > datetime.utcnow()
            ).count(),
            'expired_otps': OTP.query.filter(
                OTP.expires_at < datetime.utcnow()
            ).count(),
            'used_otps': OTP.query.filter_by(is_used=True).count(),
            'otps_today': OTP.query.filter(
                func.date(OTP.created_at) == datetime.utcnow().date()
            ).count()
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting OTP statistics: {e}")
        return {}
