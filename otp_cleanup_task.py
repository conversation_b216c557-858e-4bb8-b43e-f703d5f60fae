#!/usr/bin/env python3
"""
OTP Cleanup Task for Flask School Management System
-------------------------------------------------
Periodic cleanup task for expired OTPs and security maintenance.
Run this script periodically (e.g., via cron job) to maintain system security.
"""

import sys
import os
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from otp_security import schedule_cleanup_tasks, get_otp_statistics
from models import db
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('otp_cleanup.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def main():
    """Main cleanup task function."""
    logger.info("Starting OTP cleanup task...")
    
    try:
        # Create Flask app context
        app = create_app()
        
        with app.app_context():
            # Get statistics before cleanup
            stats_before = get_otp_statistics()
            logger.info(f"Statistics before cleanup: {stats_before}")
            
            # Run cleanup tasks
            cleanup_success = schedule_cleanup_tasks()
            
            if cleanup_success:
                # Get statistics after cleanup
                stats_after = get_otp_statistics()
                logger.info(f"Statistics after cleanup: {stats_after}")
                
                # Calculate cleanup results
                otps_removed = stats_before.get('expired_otps', 0) - stats_after.get('expired_otps', 0)
                
                logger.info(f"Cleanup completed successfully. Removed {otps_removed} expired OTPs.")
                
                # Print summary
                print(f"\n{'='*50}")
                print(f"OTP Cleanup Task - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"{'='*50}")
                print(f"Expired OTPs removed: {otps_removed}")
                print(f"Active OTPs: {stats_after.get('active_otps', 0)}")
                print(f"Total OTPs generated today: {stats_after.get('otps_today', 0)}")
                print(f"{'='*50}\n")
                
                return True
            else:
                logger.error("Cleanup task failed")
                return False
                
    except Exception as e:
        logger.error(f"Error during cleanup task: {e}")
        return False

def show_statistics():
    """Show current OTP statistics."""
    try:
        app = create_app()
        
        with app.app_context():
            stats = get_otp_statistics()
            
            print(f"\n{'='*50}")
            print(f"OTP Statistics - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"{'='*50}")
            print(f"Total OTPs generated: {stats.get('total_otps_generated', 0)}")
            print(f"Active OTPs: {stats.get('active_otps', 0)}")
            print(f"Expired OTPs: {stats.get('expired_otps', 0)}")
            print(f"Used OTPs: {stats.get('used_otps', 0)}")
            print(f"OTPs generated today: {stats.get('otps_today', 0)}")
            print(f"{'='*50}\n")
            
            return True
            
    except Exception as e:
        logger.error(f"Error getting statistics: {e}")
        return False

def test_email_config():
    """Test email configuration."""
    try:
        from email_service import test_email_configuration
        
        app = create_app()
        
        with app.app_context():
            print("\nTesting email configuration...")
            
            if test_email_configuration():
                print("✅ Email configuration test passed!")
                return True
            else:
                print("❌ Email configuration test failed!")
                return False
                
    except Exception as e:
        logger.error(f"Error testing email configuration: {e}")
        print(f"❌ Email configuration test error: {e}")
        return False

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='OTP Cleanup and Management Tool')
    parser.add_argument('--cleanup', action='store_true', help='Run cleanup task')
    parser.add_argument('--stats', action='store_true', help='Show OTP statistics')
    parser.add_argument('--test-email', action='store_true', help='Test email configuration')
    parser.add_argument('--all', action='store_true', help='Run all tasks')
    
    args = parser.parse_args()
    
    success = True
    
    if args.all or args.stats:
        print("📊 Showing OTP statistics...")
        success &= show_statistics()
    
    if args.all or args.test_email:
        print("📧 Testing email configuration...")
        success &= test_email_config()
    
    if args.all or args.cleanup:
        print("🧹 Running cleanup task...")
        success &= main()
    
    if not any([args.cleanup, args.stats, args.test_email, args.all]):
        # Default action is cleanup
        success = main()
    
    sys.exit(0 if success else 1)
