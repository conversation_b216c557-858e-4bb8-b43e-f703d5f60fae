#!/usr/bin/env python3
"""
Comprehensive OTP System Test for Flask School Management System
---------------------------------------------------------------
Tests the complete OTP verification system including email sending,
database operations, security features, and integration with login flow.
"""

import sys
import os
import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, OTP
from email_service import create_and_send_otp, send_otp_email
from otp_security import validate_otp_security, cleanup_expired_otps, get_otp_statistics
import json

class OTPSystemTestCase(unittest.TestCase):
    """Test case for OTP system functionality."""
    
    def setUp(self):
        """Set up test environment."""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        self.client = self.app.test_client()
        
        # Create database tables
        db.create_all()
        
        # Create test user
        self.test_user = User(
            username='testuser',
            email='<EMAIL>',
            role='student',
            is_approved=True
        )
        self.test_user.set_password('TestPassword123!')
        db.session.add(self.test_user)
        db.session.commit()
        
    def tearDown(self):
        """Clean up test environment."""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_otp_model_creation(self):
        """Test OTP model creation and basic functionality."""
        print("🧪 Testing OTP model creation...")
        
        # Create OTP
        otp = OTP(user_id=self.test_user.id, expiry_minutes=5)
        db.session.add(otp)
        db.session.commit()
        
        # Test OTP properties
        self.assertEqual(len(otp.otp_code), 6)
        self.assertTrue(otp.otp_code.isdigit())
        self.assertTrue(otp.is_valid())
        self.assertEqual(otp.attempts, 0)
        self.assertFalse(otp.is_used)
        
        print("✅ OTP model creation test passed")
    
    def test_otp_verification(self):
        """Test OTP verification process."""
        print("🧪 Testing OTP verification...")
        
        # Create OTP
        otp = OTP(user_id=self.test_user.id, expiry_minutes=5)
        db.session.add(otp)
        db.session.commit()
        
        correct_code = otp.otp_code
        
        # Test correct verification
        self.assertTrue(otp.verify(correct_code))
        self.assertTrue(otp.is_used)
        
        # Create new OTP for failed verification test
        otp2 = OTP(user_id=self.test_user.id, expiry_minutes=5)
        db.session.add(otp2)
        db.session.commit()
        
        # Test incorrect verification
        self.assertFalse(otp2.verify('000000'))
        self.assertEqual(otp2.attempts, 1)
        self.assertFalse(otp2.is_used)
        
        print("✅ OTP verification test passed")
    
    def test_otp_expiry(self):
        """Test OTP expiry functionality."""
        print("🧪 Testing OTP expiry...")
        
        # Create expired OTP
        otp = OTP(user_id=self.test_user.id, expiry_minutes=5)
        # Manually set expiry to past
        otp.expires_at = datetime.utcnow() - timedelta(minutes=1)
        db.session.add(otp)
        db.session.commit()
        
        # Test that expired OTP is not valid
        self.assertFalse(otp.is_valid())
        self.assertFalse(otp.verify(otp.otp_code))
        
        print("✅ OTP expiry test passed")
    
    def test_otp_cleanup(self):
        """Test OTP cleanup functionality."""
        print("🧪 Testing OTP cleanup...")
        
        # Create expired OTP
        expired_otp = OTP(user_id=self.test_user.id, expiry_minutes=5)
        expired_otp.expires_at = datetime.utcnow() - timedelta(minutes=1)
        db.session.add(expired_otp)
        
        # Create valid OTP
        valid_otp = OTP(user_id=self.test_user.id, expiry_minutes=5)
        db.session.add(valid_otp)
        db.session.commit()
        
        # Test cleanup
        removed_count = cleanup_expired_otps()
        self.assertEqual(removed_count, 1)
        
        # Verify only valid OTP remains
        remaining_otps = OTP.query.all()
        self.assertEqual(len(remaining_otps), 1)
        self.assertEqual(remaining_otps[0].id, valid_otp.id)
        
        print("✅ OTP cleanup test passed")
    
    def test_security_validation(self):
        """Test OTP security validation."""
        print("🧪 Testing OTP security validation...")
        
        # Create OTP
        otp = OTP(user_id=self.test_user.id, expiry_minutes=5)
        db.session.add(otp)
        db.session.commit()
        
        # Test valid code
        result = validate_otp_security(self.test_user.id, otp.otp_code)
        self.assertTrue(result['success'])
        self.assertEqual(result['code'], 'SUCCESS')
        
        # Create new OTP for invalid code test
        otp2 = OTP(user_id=self.test_user.id, expiry_minutes=5)
        db.session.add(otp2)
        db.session.commit()
        
        # Test invalid code
        result = validate_otp_security(self.test_user.id, '000000')
        self.assertFalse(result['success'])
        self.assertEqual(result['code'], 'INVALID_CODE')
        
        # Test invalid format
        result = validate_otp_security(self.test_user.id, 'abc123')
        self.assertFalse(result['success'])
        self.assertEqual(result['code'], 'INVALID_FORMAT')
        
        print("✅ OTP security validation test passed")
    
    @patch('email_service.Mail')
    def test_email_sending(self, mock_mail):
        """Test OTP email sending functionality."""
        print("🧪 Testing OTP email sending...")
        
        # Mock mail instance
        mock_mail_instance = MagicMock()
        mock_mail.return_value = mock_mail_instance
        
        with patch('email_service.get_mail_instance', return_value=mock_mail_instance):
            # Test email sending
            result = send_otp_email(self.test_user.id, '123456')
            self.assertTrue(result)
            
            # Verify mail.send was called
            mock_mail_instance.send.assert_called_once()
        
        print("✅ OTP email sending test passed")
    
    @patch('email_service.send_otp_email')
    def test_create_and_send_otp(self, mock_send_email):
        """Test OTP creation and sending integration."""
        print("🧪 Testing OTP creation and sending integration...")
        
        # Mock successful email sending
        mock_send_email.return_value = True
        
        # Test OTP creation and sending
        success, otp, message = create_and_send_otp(self.test_user.id)
        
        self.assertTrue(success)
        self.assertIsNotNone(otp)
        self.assertEqual(message, "OTP sent successfully")
        
        # Verify OTP was created in database
        db_otp = OTP.query.filter_by(user_id=self.test_user.id).first()
        self.assertIsNotNone(db_otp)
        self.assertEqual(db_otp.id, otp.id)
        
        print("✅ OTP creation and sending integration test passed")
    
    def test_login_flow_integration(self):
        """Test OTP integration with login flow."""
        print("🧪 Testing login flow integration...")
        
        with patch('email_service.send_otp_email', return_value=True):
            # Test login with correct credentials
            response = self.client.post('/login', data={
                'username': 'testuser',
                'password': 'TestPassword123!'
            }, follow_redirects=False)
            
            # Should redirect to OTP verification
            self.assertEqual(response.status_code, 302)
            self.assertIn('/verify-otp', response.location)
            
            # Test OTP verification page
            response = self.client.get('/verify-otp')
            self.assertEqual(response.status_code, 200)
            self.assertIn(b'verification code', response.data.lower())
        
        print("✅ Login flow integration test passed")
    
    def test_statistics(self):
        """Test OTP statistics functionality."""
        print("🧪 Testing OTP statistics...")
        
        # Create various OTPs
        otp1 = OTP(user_id=self.test_user.id, expiry_minutes=5)
        otp2 = OTP(user_id=self.test_user.id, expiry_minutes=5)
        otp2.is_used = True
        
        expired_otp = OTP(user_id=self.test_user.id, expiry_minutes=5)
        expired_otp.expires_at = datetime.utcnow() - timedelta(minutes=1)
        
        db.session.add_all([otp1, otp2, expired_otp])
        db.session.commit()
        
        # Get statistics
        stats = get_otp_statistics()
        
        self.assertEqual(stats['total_otps_generated'], 3)
        self.assertEqual(stats['active_otps'], 1)
        self.assertEqual(stats['expired_otps'], 1)
        self.assertEqual(stats['used_otps'], 1)
        
        print("✅ OTP statistics test passed")

def run_integration_test():
    """Run a complete integration test of the OTP system."""
    print("\n" + "="*60)
    print("🚀 STARTING OTP SYSTEM INTEGRATION TEST")
    print("="*60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(OTPSystemTestCase)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "="*60)
    print("📊 TEST SUMMARY")
    print("="*60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n✅ ALL TESTS PASSED! OTP system is working correctly.")
    else:
        print("\n❌ SOME TESTS FAILED! Please check the issues above.")
    
    print("="*60)
    
    return success

if __name__ == '__main__':
    success = run_integration_test()
    sys.exit(0 if success else 1)
